# Min-Max Limit Validation Fix for Merchant Dashboard SendMoney

## Problem Identified

There was an inconsistency in min-max limit validation between two money sending interfaces:

**Working correctly:**
- Home page widget: Properly validates min-max limits when users enter receiving amounts

**Not working correctly:**
- Merchant dashboard (user/sendMoney page): Missing min-max limit validation when "Receive" option is selected

## Root Cause Analysis

1. **Home Page Widget (Working):**
   - Uses Vue.js frontend validation
   - Calculates send amount from receive amount using `sendValue()` method
   - Validates calculated send amount against `sendFrom.minimum_amount` and `sendFrom.maximum_amount`
   - Disables continue button when limits are exceeded

2. **Merchant Dashboard (Broken):**
   - Uses `ajaxMoneyCalculation` method in `FrontendController.php`
   - The method calculated amounts correctly but **completely lacked min-max limit validation**
   - When `sendReceive == "receive"`, it calculated `send_amount = round($amount / $rate, 2)` but never validated this against country limits

3. **Other Controllers Had Proper Validation:**
   - `User\HomeController` (lines 87-95)
   - `API\BasicApiController` (lines 221-226) 
   - `API\MerchantRemittanceController` (lines 225-230)

## Solution Implemented

### Changes Made to `app/Http/Controllers/FrontendController.php`

1. **Added `maximum_amount` to Country query** (line 84):
   ```php
   // Before
   $country = Country::select('id','name', 'slug','code','minimum_amount','rate','facilities','image')
   
   // After  
   $country = Country::select('id','name', 'slug','code','minimum_amount','maximum_amount','rate','facilities','image')
   ```

2. **Added validation for "send" option** (lines 117-123):
   ```php
   if($request->sendReceive == "send"){
       $data['send_amount'] = $amount;
       
       // Validate send amount against sending country limits
       if ($amount < $data['sendCountry']->minimum_amount) {
           return response()->json(['errors'=> ['amount'=>['Minimum amount ' . getAmount($data['sendCountry']->minimum_amount, config('basic.fraction_number')) . " " . $data['sendCountry']->code]]]);
       }
       if ($amount > $data['sendCountry']->maximum_amount) {
           return response()->json(['errors'=> ['amount'=>['Maximum amount ' . getAmount($data['sendCountry']->maximum_amount, config('basic.fraction_number')) . " " . $data['sendCountry']->code]]]);
       }
       
       // ... rest of calculation
   }
   ```

3. **Added validation for "receive" option** (lines 133-139):
   ```php
   if($request->sendReceive == "receive"){
       $data['send_amount'] = round($amount / $rate,2);
       
       // Validate calculated send amount against sending country limits
       if ($data['send_amount'] < $data['sendCountry']->minimum_amount) {
           return response()->json(['errors'=> ['amount'=>['Minimum send amount ' . getAmount($data['sendCountry']->minimum_amount, config('basic.fraction_number')) . " " . $data['sendCountry']->code . ' (calculated from receive amount)']]]);
       }
       if ($data['send_amount'] > $data['sendCountry']->maximum_amount) {
           return response()->json(['errors'=> ['amount'=>['Maximum send amount ' . getAmount($data['sendCountry']->maximum_amount, config('basic.fraction_number')) . " " . $data['sendCountry']->code . ' (calculated from receive amount)']]]);
       }
       
       // ... rest of calculation
   }
   ```

## How the Fix Works

### For "Send" Option:
- User enters amount in sending currency
- System validates entered amount directly against sending country's min/max limits
- Shows error if amount is outside limits

### For "Receive" Option (The Main Fix):
- User enters amount in receiving currency  
- System calculates equivalent sending amount: `send_amount = receive_amount / exchange_rate`
- System validates the **calculated sending amount** against sending country's min/max limits
- Shows error with clear message indicating the limit applies to the calculated send amount

## Manual Testing Instructions

### Test Case 1: Valid Receive Amount
1. Go to merchant dashboard sendMoney page
2. Select "Receive" from dropdown
3. Enter a receive amount that results in a send amount within limits
4. **Expected:** Calculation succeeds, shows breakdown

### Test Case 2: Receive Amount Too Small
1. Select "Receive" from dropdown  
2. Enter a small receive amount that results in send amount below minimum
3. **Expected:** Error message: "Minimum send amount X USD (calculated from receive amount)"

### Test Case 3: Receive Amount Too Large
1. Select "Receive" from dropdown
2. Enter a large receive amount that results in send amount above maximum  
3. **Expected:** Error message: "Maximum send amount X USD (calculated from receive amount)"

### Test Case 4: Send Option Still Works
1. Select "Send" from dropdown
2. Test with amounts below minimum, within range, and above maximum
3. **Expected:** Proper validation messages for send amounts

## Files Modified

1. `app/Http/Controllers/FrontendController.php` - Added min-max validation logic
2. `tests/Feature/AjaxMoneyCalculationTest.php` - Created comprehensive test suite (new file)
3. `FIX_SUMMARY.md` - This documentation (new file)

## Validation Logic Consistency

The fix ensures that both interfaces now follow the same validation pattern:
- **Always validate the sending amount** against the sending country's limits
- When user enters receiving amount, calculate the sending amount first, then validate it
- Provide clear error messages indicating whether the limit applies to entered or calculated amounts

This matches the behavior of the working home page widget and other controllers in the system.
